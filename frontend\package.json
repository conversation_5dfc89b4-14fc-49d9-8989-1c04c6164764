{"name": "flowforge-ai-frontend", "version": "1.0.0", "description": "FlowForge AI - Visual Workflow Automation Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "react-flow-renderer": "^10.3.0", "reactflow": "^11.10.0", "react-dnd": "^16.0.0", "react-dnd-html5-backend": "^16.0.0", "zustand": "^4.4.0", "@tanstack/react-query": "^5.0.0", "socket.io-client": "^4.7.0", "axios": "^1.6.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "framer-motion": "^10.16.0", "react-hot-toast": "^2.4.0", "date-fns": "^2.30.0", "lucide-react": "^0.292.0", "react-syntax-highlighter": "^15.5.0", "monaco-editor": "^0.44.0", "@monaco-editor/react": "^4.6.0", "react-markdown": "^9.0.0", "remark-gfm": "^4.0.0", "recharts": "^2.8.0", "react-beautiful-dnd": "^13.1.0", "react-virtualized": "^9.22.0", "react-window": "^1.8.0", "react-intersection-observer": "^9.5.0"}, "devDependencies": {"@types/react-beautiful-dnd": "^13.1.0", "@types/react-syntax-highlighter": "^15.5.0", "@types/react-virtualized": "^9.21.0", "@types/react-window": "^1.8.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/user-event": "^14.5.0", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}