'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  PaperAirplaneIcon,
  SparklesIcon,
  XMarkIcon,
  ArrowPathIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { WorkflowNode, WorkflowEdge, AIWorkflowRequest, AIWorkflowResponse } from '@/types/workflow';
import { useAI } from '@/hooks/useAI';
import { toast } from 'react-hot-toast';

interface AIChatProps {
  workflowId?: string;
  currentNodes: WorkflowNode[];
  currentEdges: WorkflowEdge[];
  onWorkflowGenerated: (workflow: { nodes: WorkflowNode[]; edges: WorkflowEdge[] }) => void;
  onClose: () => void;
  className?: string;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  workflow?: { nodes: WorkflowNode[]; edges: WorkflowEdge[] };
  suggestions?: string[];
  confidence?: number;
}

const predefinedPrompts = [
  {
    title: 'Email Automation',
    description: 'Create a workflow that sends welcome emails to new users',
    prompt: 'Create a workflow that triggers when a new user signs up, waits 5 minutes, then sends a personalized welcome email with their name and account details.',
  },
  {
    title: 'Data Processing',
    description: 'Process CSV files and save to database',
    prompt: 'Build a workflow that reads a CSV file, validates the data, transforms it to match our database schema, and saves it to a PostgreSQL database.',
  },
  {
    title: 'Social Media Monitor',
    description: 'Monitor mentions and send notifications',
    prompt: 'Create a workflow that monitors Twitter mentions of our brand every hour, filters for positive sentiment, and sends a Slack notification with the tweet details.',
  },
  {
    title: 'Invoice Processing',
    description: 'Automate invoice approval workflow',
    prompt: 'Design a workflow that receives invoice PDFs via email, extracts key information, routes to appropriate approver based on amount, and updates our accounting system.',
  },
];

export const AIChat: React.FC<AIChatProps> = ({
  workflowId,
  currentNodes,
  currentEdges,
  onWorkflowGenerated,
  onClose,
  className = '',
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'system',
      content: 'Hi! I\'m your AI workflow assistant. I can help you create workflows from natural language descriptions, optimize existing workflows, and suggest improvements. What would you like to build today?',
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const { generateWorkflow, getSuggestions, optimizeWorkflow } = useAI();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Determine the type of request based on content
      let response: AIWorkflowResponse;
      
      if (content.toLowerCase().includes('optimize') || content.toLowerCase().includes('improve')) {
        // Optimization request
        response = await optimizeWorkflow({
          workflowId: workflowId || '',
          optimizationGoals: ['performance', 'reliability'],
        });
      } else if (currentNodes.length > 0 && (content.toLowerCase().includes('suggest') || content.toLowerCase().includes('add'))) {
        // Suggestion request
        response = await getSuggestions({
          workflowId: workflowId || '',
          currentNodes,
          currentEdges,
          context: content,
        });
      } else {
        // Workflow generation request
        const request: AIWorkflowRequest = {
          description: content,
          context: currentNodes.length > 0 ? `Current workflow has ${currentNodes.length} nodes` : undefined,
          preferences: {
            complexity: 'medium',
            includeErrorHandling: true,
          },
        };
        response = await generateWorkflow(request);
      }

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: response.workflow ? 
          'I\'ve generated a workflow based on your description. You can preview it below and apply it to your canvas.' :
          'I understand what you\'re looking for. Here are some suggestions to help you build your workflow.',
        timestamp: new Date(),
        workflow: response.workflow ? {
          nodes: response.workflow.nodes || [],
          edges: response.workflow.edges || [],
        } : undefined,
        suggestions: response.suggestions,
        confidence: response.confidence,
      };

      setMessages(prev => [...prev, aiMessage]);

      if (response.confidence && response.confidence < 0.7) {
        toast.warning('I\'m not entirely confident about this workflow. Please review it carefully.');
      }

    } catch (error) {
      console.error('AI request failed:', error);
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'I apologize, but I encountered an error while processing your request. Please try rephrasing your question or try again later.',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
      toast.error('Failed to process AI request');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(inputValue);
    }
  };

  const handleApplyWorkflow = (workflow: { nodes: WorkflowNode[]; edges: WorkflowEdge[] }) => {
    onWorkflowGenerated(workflow);
    toast.success('Workflow applied to canvas');
  };

  const handlePredefinedPrompt = (prompt: string) => {
    setInputValue(prompt);
    inputRef.current?.focus();
  };

  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'text-gray-500';
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceIcon = (confidence?: number) => {
    if (!confidence) return null;
    if (confidence >= 0.8) return <CheckCircleIcon className="w-4 h-4" />;
    if (confidence >= 0.6) return <ExclamationTriangleIcon className="w-4 h-4" />;
    return <ExclamationTriangleIcon className="w-4 h-4" />;
  };

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <SparklesIcon className="w-5 h-5 text-primary-600" />
          <h2 className="text-lg font-semibold text-gray-900">AI Assistant</h2>
        </div>
        <button
          onClick={onClose}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <XMarkIcon className="w-5 h-5" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.type === 'user'
                  ? 'bg-primary-600 text-white'
                  : message.type === 'system'
                  ? 'bg-blue-50 text-blue-900 border border-blue-200'
                  : 'bg-gray-100 text-gray-900'
              }`}
            >
              <p className="text-sm whitespace-pre-wrap">{message.content}</p>
              
              {/* Confidence indicator */}
              {message.confidence && (
                <div className={`flex items-center space-x-1 mt-2 text-xs ${getConfidenceColor(message.confidence)}`}>
                  {getConfidenceIcon(message.confidence)}
                  <span>Confidence: {Math.round(message.confidence * 100)}%</span>
                </div>
              )}

              {/* Suggestions */}
              {message.suggestions && message.suggestions.length > 0 && (
                <div className="mt-3 space-y-1">
                  <p className="text-xs font-medium text-gray-600">Suggestions:</p>
                  {message.suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSendMessage(suggestion)}
                      className="block w-full text-left text-xs p-2 bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}

              {/* Workflow preview */}
              {message.workflow && (
                <div className="mt-3 p-3 bg-white border border-gray-200 rounded">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-xs font-medium text-gray-600">Generated Workflow</p>
                    <button
                      onClick={() => handleApplyWorkflow(message.workflow!)}
                      className="text-xs bg-primary-600 text-white px-2 py-1 rounded hover:bg-primary-700 transition-colors"
                    >
                      Apply to Canvas
                    </button>
                  </div>
                  <div className="text-xs text-gray-500">
                    {message.workflow.nodes.length} nodes, {message.workflow.edges.length} connections
                  </div>
                </div>
              )}

              <div className="text-xs opacity-70 mt-2">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <ArrowPathIcon className="w-4 h-4 animate-spin text-gray-600" />
                <span className="text-sm text-gray-600">AI is thinking...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Predefined Prompts */}
      {messages.length === 1 && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-1 mb-3">
            <LightBulbIcon className="w-4 h-4 text-yellow-600" />
            <p className="text-sm font-medium text-gray-700">Quick Start Ideas</p>
          </div>
          <div className="grid grid-cols-1 gap-2">
            {predefinedPrompts.map((prompt, index) => (
              <button
                key={index}
                onClick={() => handlePredefinedPrompt(prompt.prompt)}
                className="text-left p-2 bg-white border border-gray-200 rounded hover:border-primary-300 hover:bg-primary-50 transition-colors"
              >
                <p className="text-sm font-medium text-gray-900">{prompt.title}</p>
                <p className="text-xs text-gray-500 mt-1">{prompt.description}</p>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <textarea
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Describe the workflow you want to create..."
            className="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            rows={2}
            disabled={isLoading}
          />
          <button
            onClick={() => handleSendMessage(inputValue)}
            disabled={!inputValue.trim() || isLoading}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <PaperAirplaneIcon className="w-4 h-4" />
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Press Enter to send, Shift+Enter for new line
        </p>
      </div>
    </div>
  );
};
