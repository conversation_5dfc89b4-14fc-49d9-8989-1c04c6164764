# FlowForge AI - Docker Compose Configuration
# Complete multi-container setup for development and production

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: flowforge-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-flowforge_ai}
      POSTGRES_USER: ${POSTGRES_USER:-flowforge}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-flowforge_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ../../database/seeds:/docker-entrypoint-initdb.d/seeds
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - flowforge-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-flowforge} -d ${POSTGRES_DB:-flowforge_ai}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache & Queue
  redis:
    image: redis:7-alpine
    container_name: flowforge-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
      - ../../config/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - flowforge-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Backend API Server
  backend:
    build:
      context: ../../backend
      dockerfile: ../deployment/docker/Dockerfile.backend
      args:
        NODE_ENV: ${NODE_ENV:-development}
    container_name: flowforge-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      PORT: 8000
      DATABASE_URL: postgresql://${POSTGRES_USER:-flowforge}:${POSTGRES_PASSWORD:-flowforge_password}@postgres:5432/${POSTGRES_DB:-flowforge_ai}
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_password}@redis:6379
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3000}
      HUGGING_FACE_API_KEY: ${HUGGING_FACE_API_KEY}
      EMAIL_SERVICE: ${EMAIL_SERVICE:-smtp}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      FILE_UPLOAD_PATH: /app/uploads
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-10485760}
      RATE_LIMIT_WINDOW: ${RATE_LIMIT_WINDOW:-900000}
      RATE_LIMIT_MAX: ${RATE_LIMIT_MAX:-100}
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    networks:
      - flowforge-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ../../frontend
      dockerfile: ../deployment/docker/Dockerfile.frontend
      args:
        NODE_ENV: ${NODE_ENV:-development}
        NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:8000}
        NEXT_PUBLIC_WS_URL: ${NEXT_PUBLIC_WS_URL:-ws://localhost:8000}
    container_name: flowforge-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:8000}
      NEXT_PUBLIC_WS_URL: ${NEXT_PUBLIC_WS_URL:-ws://localhost:8000}
      NEXT_PUBLIC_APP_NAME: ${NEXT_PUBLIC_APP_NAME:-FlowForge AI}
      NEXT_PUBLIC_APP_VERSION: ${NEXT_PUBLIC_APP_VERSION:-1.0.0}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    networks:
      - flowforge-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Processing Service
  ai-service:
    build:
      context: ../../ai-service
      dockerfile: ../deployment/docker/Dockerfile.ai-service
    container_name: flowforge-ai-service
    restart: unless-stopped
    environment:
      PYTHON_ENV: ${PYTHON_ENV:-development}
      API_PORT: 8001
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_password}@redis:6379
      HUGGING_FACE_API_KEY: ${HUGGING_FACE_API_KEY}
      MODEL_CACHE_DIR: /app/models
      MAX_WORKERS: ${AI_MAX_WORKERS:-2}
      TIMEOUT_SECONDS: ${AI_TIMEOUT:-30}
    volumes:
      - ai_models:/app/models
      - ai_logs:/app/logs
    ports:
      - "${AI_SERVICE_PORT:-8001}:8001"
    networks:
      - flowforge-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Workflow Scheduler Service
  scheduler:
    build:
      context: ../../scheduler
      dockerfile: ../deployment/docker/Dockerfile.scheduler
    container_name: flowforge-scheduler
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      DATABASE_URL: postgresql://${POSTGRES_USER:-flowforge}:${POSTGRES_PASSWORD:-flowforge_password}@postgres:5432/${POSTGRES_DB:-flowforge_ai}
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_password}@redis:6379
      BACKEND_URL: http://backend:8000
      SCHEDULER_INTERVAL: ${SCHEDULER_INTERVAL:-60000}
      MAX_CONCURRENT_JOBS: ${MAX_CONCURRENT_JOBS:-10}
    volumes:
      - scheduler_logs:/app/logs
    networks:
      - flowforge-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      backend:
        condition: service_healthy

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: flowforge-nginx
    restart: unless-stopped
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ../../config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ../../config/nginx/sites-available:/etc/nginx/sites-available:ro
      - nginx_logs:/var/log/nginx
      - ssl_certs:/etc/nginx/ssl:ro
    networks:
      - flowforge-network
    depends_on:
      - frontend
      - backend
    profiles:
      - production

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: flowforge-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ../../config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    networks:
      - flowforge-network
    profiles:
      - monitoring

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: flowforge-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ../../config/grafana/provisioning:/etc/grafana/provisioning:ro
      - ../../config/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    networks:
      - flowforge-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

  # Log aggregation with ELK Stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: flowforge-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "${ELASTICSEARCH_PORT:-9200}:9200"
    networks:
      - flowforge-network
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: flowforge-logstash
    restart: unless-stopped
    volumes:
      - ../../config/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ../../config/logstash/config:/usr/share/logstash/config:ro
      - backend_logs:/app/logs/backend:ro
      - ai_logs:/app/logs/ai:ro
      - scheduler_logs:/app/logs/scheduler:ro
      - nginx_logs:/app/logs/nginx:ro
    networks:
      - flowforge-network
    depends_on:
      - elasticsearch
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: flowforge-kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "${KIBANA_PORT:-5601}:5601"
    networks:
      - flowforge-network
    depends_on:
      - elasticsearch
    profiles:
      - logging

# Named volumes for data persistence
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
  ai_models:
    driver: local
  ai_logs:
    driver: local
  scheduler_logs:
    driver: local
  nginx_logs:
    driver: local
  ssl_certs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

# Custom network for service communication
networks:
  flowforge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
