'use client';

import React, { useState, useMemo } from 'react';
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  PlayIcon,
  CogIcon,
  QuestionMarkCircleIcon,
  ArrowPathIcon,
  ClockIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  CircleStackIcon,
  CommandLineIcon,
  ArrowsRightLeftIcon,
  FunnelIcon as FilterIcon,
  PlusIcon,
  DocumentDuplicateIcon,
} from '@heroicons/react/24/outline';
import { NodeType, NodeLibraryItem } from '@/types/workflow';

interface NodeLibraryProps {
  onNodeDrag: (nodeType: NodeType) => void;
  className?: string;
}

const nodeLibraryItems: NodeLibraryItem[] = [
  {
    type: 'trigger',
    name: 'Trigger',
    description: 'Start your workflow with various triggers',
    icon: 'PlayIcon',
    color: 'bg-green-500',
    category: 'Triggers',
    inputs: [],
    outputs: [
      { id: 'output', name: 'Output', type: 'any', description: 'Trigger output data' }
    ],
    configSchema: {
      triggerType: { type: 'select', options: ['manual', 'schedule', 'webhook', 'email'] },
      schedule: { type: 'string', condition: 'triggerType === "schedule"' },
    },
  },
  {
    type: 'action',
    name: 'Action',
    description: 'Perform actions and operations',
    icon: 'CogIcon',
    color: 'bg-blue-500',
    category: 'Actions',
    inputs: [
      { id: 'input', name: 'Input', type: 'any', required: true }
    ],
    outputs: [
      { id: 'output', name: 'Output', type: 'any', description: 'Action result' }
    ],
    configSchema: {
      actionType: { type: 'select', options: ['http', 'script', 'transform'] },
      config: { type: 'object' },
    },
  },
  {
    type: 'condition',
    name: 'Condition',
    description: 'Add conditional logic to your workflow',
    icon: 'QuestionMarkCircleIcon',
    color: 'bg-yellow-500',
    category: 'Logic',
    inputs: [
      { id: 'input', name: 'Input', type: 'any', required: true }
    ],
    outputs: [
      { id: 'true', name: 'True', type: 'any', description: 'When condition is true' },
      { id: 'false', name: 'False', type: 'any', description: 'When condition is false' }
    ],
    configSchema: {
      condition: { type: 'string', required: true },
      operator: { type: 'select', options: ['equals', 'not_equals', 'greater', 'less', 'contains'] },
    },
  },
  {
    type: 'loop',
    name: 'Loop',
    description: 'Repeat actions for arrays or conditions',
    icon: 'ArrowPathIcon',
    color: 'bg-purple-500',
    category: 'Logic',
    inputs: [
      { id: 'input', name: 'Input', type: 'array', required: true }
    ],
    outputs: [
      { id: 'item', name: 'Current Item', type: 'any', description: 'Current loop item' },
      { id: 'output', name: 'Final Output', type: 'array', description: 'Loop results' }
    ],
    configSchema: {
      loopType: { type: 'select', options: ['forEach', 'while', 'for'] },
      maxIterations: { type: 'number', default: 100 },
    },
  },
  {
    type: 'delay',
    name: 'Delay',
    description: 'Add delays and wait conditions',
    icon: 'ClockIcon',
    color: 'bg-orange-500',
    category: 'Utilities',
    inputs: [
      { id: 'input', name: 'Input', type: 'any', required: false }
    ],
    outputs: [
      { id: 'output', name: 'Output', type: 'any', description: 'Delayed output' }
    ],
    configSchema: {
      delayType: { type: 'select', options: ['fixed', 'dynamic'] },
      duration: { type: 'number', required: true },
      unit: { type: 'select', options: ['seconds', 'minutes', 'hours'] },
    },
  },
  {
    type: 'webhook',
    name: 'Webhook',
    description: 'Send and receive HTTP webhooks',
    icon: 'GlobeAltIcon',
    color: 'bg-indigo-500',
    category: 'Integrations',
    inputs: [
      { id: 'input', name: 'Input', type: 'object', required: false }
    ],
    outputs: [
      { id: 'output', name: 'Response', type: 'object', description: 'Webhook response' }
    ],
    configSchema: {
      method: { type: 'select', options: ['GET', 'POST', 'PUT', 'DELETE'] },
      url: { type: 'string', required: true },
      headers: { type: 'object' },
      body: { type: 'object' },
    },
  },
  {
    type: 'email',
    name: 'Email',
    description: 'Send emails and notifications',
    icon: 'EnvelopeIcon',
    color: 'bg-red-500',
    category: 'Communications',
    inputs: [
      { id: 'input', name: 'Input', type: 'object', required: true }
    ],
    outputs: [
      { id: 'output', name: 'Result', type: 'object', description: 'Email send result' }
    ],
    configSchema: {
      to: { type: 'string', required: true },
      subject: { type: 'string', required: true },
      body: { type: 'string', required: true },
      template: { type: 'string' },
    },
  },
  {
    type: 'database',
    name: 'Database',
    description: 'Query and manipulate databases',
    icon: 'CircleStackIcon',
    color: 'bg-teal-500',
    category: 'Data',
    inputs: [
      { id: 'input', name: 'Query Data', type: 'object', required: false }
    ],
    outputs: [
      { id: 'output', name: 'Query Result', type: 'array', description: 'Database query result' }
    ],
    configSchema: {
      operation: { type: 'select', options: ['select', 'insert', 'update', 'delete'] },
      table: { type: 'string', required: true },
      query: { type: 'string' },
      connection: { type: 'string', required: true },
    },
  },
  {
    type: 'api',
    name: 'API Call',
    description: 'Make HTTP API requests',
    icon: 'CommandLineIcon',
    color: 'bg-cyan-500',
    category: 'Integrations',
    inputs: [
      { id: 'input', name: 'Request Data', type: 'object', required: false }
    ],
    outputs: [
      { id: 'output', name: 'Response', type: 'object', description: 'API response' }
    ],
    configSchema: {
      method: { type: 'select', options: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] },
      url: { type: 'string', required: true },
      headers: { type: 'object' },
      authentication: { type: 'select', options: ['none', 'bearer', 'basic', 'api-key'] },
    },
  },
  {
    type: 'transform',
    name: 'Transform',
    description: 'Transform and manipulate data',
    icon: 'ArrowsRightLeftIcon',
    color: 'bg-pink-500',
    category: 'Data',
    inputs: [
      { id: 'input', name: 'Input Data', type: 'any', required: true }
    ],
    outputs: [
      { id: 'output', name: 'Transformed Data', type: 'any', description: 'Transformed output' }
    ],
    configSchema: {
      transformType: { type: 'select', options: ['map', 'filter', 'reduce', 'custom'] },
      script: { type: 'string' },
      mapping: { type: 'object' },
    },
  },
  {
    type: 'filter',
    name: 'Filter',
    description: 'Filter data based on conditions',
    icon: 'FilterIcon',
    color: 'bg-emerald-500',
    category: 'Data',
    inputs: [
      { id: 'input', name: 'Input Array', type: 'array', required: true }
    ],
    outputs: [
      { id: 'output', name: 'Filtered Data', type: 'array', description: 'Filtered results' }
    ],
    configSchema: {
      condition: { type: 'string', required: true },
      field: { type: 'string' },
      operator: { type: 'select', options: ['equals', 'contains', 'greater', 'less'] },
    },
  },
  {
    type: 'merge',
    name: 'Merge',
    description: 'Merge multiple data streams',
    icon: 'PlusIcon',
    color: 'bg-violet-500',
    category: 'Data',
    inputs: [
      { id: 'input1', name: 'Input 1', type: 'any', required: true },
      { id: 'input2', name: 'Input 2', type: 'any', required: true }
    ],
    outputs: [
      { id: 'output', name: 'Merged Data', type: 'any', description: 'Merged output' }
    ],
    configSchema: {
      mergeType: { type: 'select', options: ['concat', 'merge', 'zip'] },
      strategy: { type: 'select', options: ['first', 'last', 'all'] },
    },
  },
  {
    type: 'split',
    name: 'Split',
    description: 'Split data into multiple streams',
    icon: 'DocumentDuplicateIcon',
    color: 'bg-rose-500',
    category: 'Data',
    inputs: [
      { id: 'input', name: 'Input Data', type: 'any', required: true }
    ],
    outputs: [
      { id: 'output1', name: 'Output 1', type: 'any', description: 'First split' },
      { id: 'output2', name: 'Output 2', type: 'any', description: 'Second split' }
    ],
    configSchema: {
      splitType: { type: 'select', options: ['duplicate', 'field', 'condition'] },
      field: { type: 'string' },
      condition: { type: 'string' },
    },
  },
];

const iconMap = {
  PlayIcon,
  CogIcon,
  QuestionMarkCircleIcon,
  ArrowPathIcon,
  ClockIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  CircleStackIcon,
  CommandLineIcon,
  ArrowsRightLeftIcon,
  FilterIcon,
  PlusIcon,
  DocumentDuplicateIcon,
};

export const NodeLibrary: React.FC<NodeLibraryProps> = ({
  onNodeDrag,
  className = '',
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = useMemo(() => {
    const cats = ['All', ...new Set(nodeLibraryItems.map(item => item.category))];
    return cats;
  }, []);

  const filteredNodes = useMemo(() => {
    return nodeLibraryItems.filter(node => {
      const matchesSearch = node.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           node.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'All' || node.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [searchTerm, selectedCategory]);

  const handleDragStart = (event: React.DragEvent, nodeType: NodeType) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
    onNodeDrag(nodeType);
  };

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap];
    return IconComponent ? <IconComponent className="w-5 h-5" /> : <CogIcon className="w-5 h-5" />;
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Node Library</h2>
        
        {/* Search */}
        <div className="relative mb-3">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search nodes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        {/* Category Filter */}
        <div className="relative">
          <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none bg-white"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Node List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {filteredNodes.map((node) => (
          <div
            key={node.type}
            draggable
            onDragStart={(e) => handleDragStart(e, node.type)}
            className="group p-3 bg-white border border-gray-200 rounded-lg cursor-move hover:shadow-md hover:border-primary-300 transition-all duration-200"
          >
            <div className="flex items-start space-x-3">
              <div className={`flex-shrink-0 w-10 h-10 ${node.color} rounded-lg flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-200`}>
                {getIcon(node.icon)}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-gray-900 group-hover:text-primary-600 transition-colors">
                  {node.name}
                </h3>
                <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                  {node.description}
                </p>
                <div className="flex items-center mt-2 space-x-2">
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    {node.category}
                  </span>
                  {node.inputs.length > 0 && (
                    <span className="text-xs text-gray-400">
                      {node.inputs.length} input{node.inputs.length !== 1 ? 's' : ''}
                    </span>
                  )}
                  {node.outputs.length > 0 && (
                    <span className="text-xs text-gray-400">
                      {node.outputs.length} output{node.outputs.length !== 1 ? 's' : ''}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}

        {filteredNodes.length === 0 && (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-2">
              <MagnifyingGlassIcon className="w-12 h-12 mx-auto" />
            </div>
            <p className="text-gray-500">No nodes found</p>
            <p className="text-sm text-gray-400 mt-1">
              Try adjusting your search or filter
            </p>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <p className="text-xs text-gray-500 text-center">
          Drag nodes to the canvas to build your workflow
        </p>
      </div>
    </div>
  );
};
