import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

import { AuthenticatedRequest } from '../middleware/auth';
import { workflowEngine } from '../services/workflowEngine';
import { collaborationService } from '../services/collaborationService';
import { logger } from '../utils/logger';
import { ApiResponse, PaginatedResponse } from '../types/api';
import { Workflow, WorkflowExecution } from '../types/workflow';

const prisma = new PrismaClient();

export class WorkflowController {
  // Get all workflows for authenticated user
  async getWorkflows(req: AuthenticatedRequest, res: Response) {
    try {
      const { page = 1, limit = 20, search, category, status, sortBy = 'updatedAt', sortOrder = 'desc' } = req.query;
      const userId = req.user!.id;

      const skip = (Number(page) - 1) * Number(limit);
      const take = Number(limit);

      // Build where clause
      const where: any = {
        OR: [
          { createdBy: userId },
          { collaborators: { some: { userId } } },
          { isPublic: true },
        ],
      };

      if (search) {
        where.OR = [
          { name: { contains: search as string, mode: 'insensitive' } },
          { description: { contains: search as string, mode: 'insensitive' } },
        ];
      }

      if (category) {
        where.category = category;
      }

      if (status) {
        where.status = status;
      }

      // Get workflows with pagination
      const [workflows, total] = await Promise.all([
        prisma.workflow.findMany({
          where,
          skip,
          take,
          orderBy: { [sortBy as string]: sortOrder },
          include: {
            creator: {
              select: { id: true, username: true, firstName: true, lastName: true, avatar: true },
            },
            collaborators: {
              include: {
                user: {
                  select: { id: true, username: true, firstName: true, lastName: true, avatar: true },
                },
              },
            },
            _count: {
              select: { executions: true, templates: true },
            },
          },
        }),
        prisma.workflow.count({ where }),
      ]);

      const response: ApiResponse<PaginatedResponse<Workflow>> = {
        success: true,
        data: {
          items: workflows as any,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            totalPages: Math.ceil(total / Number(limit)),
            hasNext: skip + take < total,
            hasPrev: Number(page) > 1,
          },
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      };

      res.json(response);
    } catch (error) {
      logger.error('Error fetching workflows:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to fetch workflows',
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      });
    }
  }

  // Get single workflow by ID
  async getWorkflow(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const workflow = await prisma.workflow.findFirst({
        where: {
          id,
          OR: [
            { createdBy: userId },
            { collaborators: { some: { userId } } },
            { isPublic: true },
          ],
        },
        include: {
          creator: {
            select: { id: true, username: true, firstName: true, lastName: true, avatar: true },
          },
          collaborators: {
            include: {
              user: {
                select: { id: true, username: true, firstName: true, lastName: true, avatar: true },
              },
            },
          },
          executions: {
            take: 10,
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      if (!workflow) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Workflow not found',
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || uuidv4(),
        });
      }

      const response: ApiResponse<Workflow> = {
        success: true,
        data: workflow as any,
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      };

      res.json(response);
    } catch (error) {
      logger.error('Error fetching workflow:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to fetch workflow',
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      });
    }
  }

  // Create new workflow
  async createWorkflow(req: AuthenticatedRequest, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            details: errors.array(),
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || uuidv4(),
        });
      }

      const { name, description, category, tags = [], isPublic = false, templateId } = req.body;
      const userId = req.user!.id;

      let initialNodes = [];
      let initialEdges = [];

      // If creating from template, copy template data
      if (templateId) {
        const template = await prisma.template.findUnique({
          where: { id: templateId },
          include: { workflow: true },
        });

        if (template) {
          initialNodes = template.workflow.nodes;
          initialEdges = template.workflow.edges;
        }
      }

      const workflow = await prisma.workflow.create({
        data: {
          id: uuidv4(),
          name,
          description,
          category,
          tags,
          isPublic,
          nodes: initialNodes,
          edges: initialEdges,
          status: 'draft',
          version: 1,
          createdBy: userId,
          metadata: {
            executionCount: 0,
            successRate: 0,
            complexity: 'simple',
            performance: {
              avgExecutionTime: 0,
              errorRate: 0,
              throughput: 0,
            },
          },
        },
        include: {
          creator: {
            select: { id: true, username: true, firstName: true, lastName: true, avatar: true },
          },
        },
      });

      // Emit real-time update
      const io = req.app.get('io');
      if (io) {
        io.to(`user:${userId}`).emit('workflow:created', workflow);
      }

      const response: ApiResponse<Workflow> = {
        success: true,
        data: workflow as any,
        message: 'Workflow created successfully',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      };

      res.status(201).json(response);
    } catch (error) {
      logger.error('Error creating workflow:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to create workflow',
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      });
    }
  }

  // Update workflow
  async updateWorkflow(req: AuthenticatedRequest, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            details: errors.array(),
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || uuidv4(),
        });
      }

      const { id } = req.params;
      const userId = req.user!.id;
      const updateData = req.body;

      // Check if user has permission to update
      const existingWorkflow = await prisma.workflow.findFirst({
        where: {
          id,
          OR: [
            { createdBy: userId },
            { collaborators: { some: { userId, role: { in: ['editor', 'admin'] } } } },
          ],
        },
      });

      if (!existingWorkflow) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Workflow not found or insufficient permissions',
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || uuidv4(),
        });
      }

      // Update workflow
      const workflow = await prisma.workflow.update({
        where: { id },
        data: {
          ...updateData,
          version: { increment: 1 },
          updatedAt: new Date(),
        },
        include: {
          creator: {
            select: { id: true, username: true, firstName: true, lastName: true, avatar: true },
          },
          collaborators: {
            include: {
              user: {
                select: { id: true, username: true, firstName: true, lastName: true, avatar: true },
              },
            },
          },
        },
      });

      // Emit real-time update to collaborators
      const io = req.app.get('io');
      if (io) {
        const collaboratorIds = workflow.collaborators.map(c => c.userId);
        const allUserIds = [workflow.createdBy, ...collaboratorIds];
        
        allUserIds.forEach(uid => {
          io.to(`user:${uid}`).emit('workflow:updated', {
            workflowId: id,
            changes: updateData,
            updatedBy: userId,
            version: workflow.version,
          });
        });
      }

      const response: ApiResponse<Workflow> = {
        success: true,
        data: workflow as any,
        message: 'Workflow updated successfully',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      };

      res.json(response);
    } catch (error) {
      logger.error('Error updating workflow:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update workflow',
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      });
    }
  }

  // Execute workflow
  async executeWorkflow(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const { input = {}, triggerSource = 'manual', scheduledFor } = req.body;
      const userId = req.user!.id;

      // Check if user has permission to execute
      const workflow = await prisma.workflow.findFirst({
        where: {
          id,
          OR: [
            { createdBy: userId },
            { collaborators: { some: { userId } } },
          ],
        },
      });

      if (!workflow) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Workflow not found or insufficient permissions',
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || uuidv4(),
        });
      }

      // Execute workflow
      const execution = await workflowEngine.executeWorkflow(id, {
        input,
        triggerSource,
        scheduledFor,
        userId,
      });

      const response: ApiResponse<WorkflowExecution> = {
        success: true,
        data: execution,
        message: 'Workflow execution started',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      };

      res.json(response);
    } catch (error) {
      logger.error('Error executing workflow:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to execute workflow',
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      });
    }
  }

  // Delete workflow
  async deleteWorkflow(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      // Check if user is the owner
      const workflow = await prisma.workflow.findFirst({
        where: {
          id,
          createdBy: userId,
        },
      });

      if (!workflow) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Workflow not found or insufficient permissions',
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || uuidv4(),
        });
      }

      // Delete workflow and related data
      await prisma.$transaction([
        prisma.workflowExecution.deleteMany({ where: { workflowId: id } }),
        prisma.workflowCollaborator.deleteMany({ where: { workflowId: id } }),
        prisma.workflow.delete({ where: { id } }),
      ]);

      // Emit real-time update
      const io = req.app.get('io');
      if (io) {
        io.to(`user:${userId}`).emit('workflow:deleted', { workflowId: id });
      }

      const response: ApiResponse<null> = {
        success: true,
        message: 'Workflow deleted successfully',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      };

      res.json(response);
    } catch (error) {
      logger.error('Error deleting workflow:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to delete workflow',
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || uuidv4(),
      });
    }
  }
}

export const workflowController = new WorkflowController();
