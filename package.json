{"name": "flowforge-ai", "version": "1.0.0", "description": "FlowForge AI - The Visual Automation Revolution. A comprehensive workflow automation platform with AI-powered natural language processing.", "private": true, "author": {"name": "FlowForge AI Team", "email": "<EMAIL>", "url": "https://flowforge-ai.com"}, "license": "MIT", "homepage": "https://github.com/HectorTa1989/flowforge-ai", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/flowforge-ai.git"}, "bugs": {"url": "https://github.com/HectorTa1989/flowforge-ai/issues"}, "keywords": ["workflow", "automation", "ai", "visual-programming", "no-code", "low-code", "drag-and-drop", "business-process", "integration", "zapier-alternative"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["frontend", "backend", "ai-service", "scheduler"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:ai\" \"npm run dev:scheduler\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:ai": "cd ai-service && python src/api_server.py", "dev:scheduler": "cd scheduler && npm run dev", "build": "npm run build:frontend && npm run build:backend && npm run build:scheduler", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:scheduler": "cd scheduler && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\" \"npm run start:scheduler\"", "start:frontend": "cd frontend && npm start", "start:backend": "cd backend && npm start", "start:scheduler": "cd scheduler && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:watch": "concurrently \"npm run test:frontend -- --watch\" \"npm run test:backend -- --watch\"", "test:coverage": "npm run test:frontend -- --coverage && npm run test:backend -- --coverage", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:fix": "npm run lint:frontend -- --fix && npm run lint:backend -- --fix", "type-check": "npm run type-check:frontend && npm run type-check:backend", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && tsc --noEmit", "db:migrate": "cd backend && npm run db:migrate", "db:generate": "cd backend && npm run db:generate", "db:seed": "cd backend && npm run db:seed", "db:studio": "cd backend && npm run db:studio", "db:reset": "cd backend && npx prisma migrate reset --force", "docker:dev": "docker-compose -f deployment/docker/docker-compose.yml up -d", "docker:prod": "docker-compose -f deployment/docker/docker-compose.yml --profile production up -d", "docker:monitoring": "docker-compose -f deployment/docker/docker-compose.yml --profile monitoring up -d", "docker:logging": "docker-compose -f deployment/docker/docker-compose.yml --profile logging up -d", "docker:down": "docker-compose -f deployment/docker/docker-compose.yml down", "docker:logs": "docker-compose -f deployment/docker/docker-compose.yml logs -f", "docker:build": "docker-compose -f deployment/docker/docker-compose.yml build", "setup": "npm run setup:install && npm run setup:env && npm run setup:db", "setup:install": "npm install && npm run install:all", "setup:env": "cp config/.env.example config/.env.development", "setup:db": "npm run db:generate && npm run db:migrate && npm run db:seed", "install:all": "cd frontend && npm install && cd ../backend && npm install && cd ../scheduler && npm install", "clean": "npm run clean:deps && npm run clean:build && npm run clean:logs", "clean:deps": "rm -rf node_modules frontend/node_modules backend/node_modules scheduler/node_modules", "clean:build": "rm -rf frontend/.next frontend/dist backend/dist scheduler/dist", "clean:logs": "rm -rf logs frontend/logs backend/logs scheduler/logs", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "security:audit": "npm audit --audit-level moderate", "security:fix": "npm audit fix", "deploy:staging": "npm run build && npm run docker:prod", "deploy:production": "npm run build && npm run docker:prod && npm run docker:monitoring", "backup:db": "pg_dump $DATABASE_URL > backups/db-$(date +%Y%m%d-%H%M%S).sql", "restore:db": "psql $DATABASE_URL < $1", "health:check": "curl -f http://localhost:8000/health && curl -f http://localhost:3000", "logs:backend": "tail -f backend/logs/app.log", "logs:frontend": "tail -f frontend/logs/app.log", "logs:docker": "docker-compose -f deployment/docker/docker-compose.yml logs -f", "monitor:start": "npm run docker:monitoring", "monitor:stop": "docker-compose -f deployment/docker/docker-compose.yml --profile monitoring down", "docs:generate": "typedoc --out docs/api backend/src && jsdoc -d docs/frontend frontend/src", "docs:serve": "http-server docs -p 8080", "release:patch": "npm version patch && git push && git push --tags", "release:minor": "npm version minor && git push && git push --tags", "release:major": "npm version major && git push && git push --tags"}, "devDependencies": {"concurrently": "^8.2.0", "prettier": "^3.0.0", "typedoc": "^0.25.0", "jsdoc": "^4.0.0", "http-server": "^14.1.0", "@types/node": "^20.0.0", "eslint": "^8.0.0", "husky": "^8.0.0", "lint-staged": "^15.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test && npm run type-check"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}