import axios from 'axios';
import { logger } from '../utils/logger';
import { 
  AIWorkflowRequest, 
  AIWorkflowResponse, 
  WorkflowNode, 
  WorkflowEdge,
  NodeType 
} from '../types/workflow';

interface HuggingFaceResponse {
  generated_text: string;
  score?: number;
}

interface WorkflowPattern {
  keywords: string[];
  nodeTypes: NodeType[];
  template: {
    nodes: Partial<WorkflowNode>[];
    edges: Partial<WorkflowEdge>[];
  };
  complexity: 'simple' | 'medium' | 'complex';
}

export class AIProcessor {
  private readonly huggingFaceApiKey: string;
  private readonly huggingFaceBaseUrl = 'https://api-inference.huggingface.co/models';
  private readonly workflowPatterns: WorkflowPattern[];

  constructor() {
    this.huggingFaceApiKey = process.env.HUGGING_FACE_API_KEY || '';
    this.workflowPatterns = this.initializeWorkflowPatterns();
  }

  private initializeWorkflowPatterns(): WorkflowPattern[] {
    return [
      {
        keywords: ['email', 'send', 'notification', 'alert', 'notify'],
        nodeTypes: ['trigger', 'condition', 'email', 'delay'],
        template: {
          nodes: [
            { type: 'trigger', data: { label: 'Email Trigger' } },
            { type: 'condition', data: { label: 'Check Conditions' } },
            { type: 'delay', data: { label: 'Wait Period' } },
            { type: 'email', data: { label: 'Send Email' } },
          ],
          edges: [
            { source: '0', target: '1' },
            { source: '1', target: '2' },
            { source: '2', target: '3' },
          ],
        },
        complexity: 'simple',
      },
      {
        keywords: ['data', 'process', 'transform', 'csv', 'database', 'import'],
        nodeTypes: ['trigger', 'transform', 'filter', 'database'],
        template: {
          nodes: [
            { type: 'trigger', data: { label: 'Data Source' } },
            { type: 'transform', data: { label: 'Transform Data' } },
            { type: 'filter', data: { label: 'Filter Records' } },
            { type: 'database', data: { label: 'Save to Database' } },
          ],
          edges: [
            { source: '0', target: '1' },
            { source: '1', target: '2' },
            { source: '2', target: '3' },
          ],
        },
        complexity: 'medium',
      },
      {
        keywords: ['api', 'webhook', 'http', 'rest', 'integration'],
        nodeTypes: ['webhook', 'condition', 'api', 'transform'],
        template: {
          nodes: [
            { type: 'webhook', data: { label: 'Webhook Trigger' } },
            { type: 'condition', data: { label: 'Validate Request' } },
            { type: 'api', data: { label: 'API Call' } },
            { type: 'transform', data: { label: 'Format Response' } },
          ],
          edges: [
            { source: '0', target: '1' },
            { source: '1', target: '2' },
            { source: '2', target: '3' },
          ],
        },
        complexity: 'medium',
      },
      {
        keywords: ['schedule', 'cron', 'daily', 'weekly', 'monthly', 'recurring'],
        nodeTypes: ['trigger', 'loop', 'condition', 'action'],
        template: {
          nodes: [
            { type: 'trigger', data: { label: 'Scheduled Trigger' } },
            { type: 'loop', data: { label: 'Process Items' } },
            { type: 'condition', data: { label: 'Check Status' } },
            { type: 'action', data: { label: 'Execute Action' } },
          ],
          edges: [
            { source: '0', target: '1' },
            { source: '1', target: '2' },
            { source: '2', target: '3' },
          ],
        },
        complexity: 'simple',
      },
      {
        keywords: ['approval', 'review', 'workflow', 'human', 'decision'],
        nodeTypes: ['trigger', 'condition', 'delay', 'email', 'condition'],
        template: {
          nodes: [
            { type: 'trigger', data: { label: 'Request Trigger' } },
            { type: 'condition', data: { label: 'Auto-approve Check' } },
            { type: 'email', data: { label: 'Send for Approval' } },
            { type: 'delay', data: { label: 'Wait for Response' } },
            { type: 'condition', data: { label: 'Process Decision' } },
          ],
          edges: [
            { source: '0', target: '1' },
            { source: '1', target: '2' },
            { source: '2', target: '3' },
            { source: '3', target: '4' },
          ],
        },
        complexity: 'complex',
      },
    ];
  }

  async generateWorkflow(request: AIWorkflowRequest): Promise<AIWorkflowResponse> {
    try {
      logger.info('Generating workflow from description:', request.description);

      // First, try pattern matching for quick results
      const patternMatch = this.matchWorkflowPattern(request.description);
      
      if (patternMatch) {
        logger.info('Found pattern match, generating workflow from template');
        return this.generateFromPattern(patternMatch, request);
      }

      // If no pattern match, use AI model
      logger.info('No pattern match found, using AI model');
      return await this.generateWithAI(request);

    } catch (error) {
      logger.error('Error generating workflow:', error);
      throw new Error('Failed to generate workflow');
    }
  }

  private matchWorkflowPattern(description: string): WorkflowPattern | null {
    const lowerDescription = description.toLowerCase();
    
    for (const pattern of this.workflowPatterns) {
      const matchCount = pattern.keywords.filter(keyword => 
        lowerDescription.includes(keyword)
      ).length;
      
      // If at least 50% of keywords match, consider it a match
      if (matchCount >= Math.ceil(pattern.keywords.length * 0.5)) {
        return pattern;
      }
    }
    
    return null;
  }

  private generateFromPattern(pattern: WorkflowPattern, request: AIWorkflowRequest): AIWorkflowResponse {
    const nodes: WorkflowNode[] = pattern.template.nodes.map((nodeTemplate, index) => ({
      id: `node-${index}`,
      type: nodeTemplate.type!,
      position: { x: 100 + (index * 200), y: 100 },
      data: {
        label: nodeTemplate.data?.label || `${nodeTemplate.type} Node`,
        config: {},
        inputs: [],
        outputs: [],
      },
    }));

    const edges: WorkflowEdge[] = pattern.template.edges.map((edgeTemplate, index) => ({
      id: `edge-${index}`,
      source: `node-${edgeTemplate.source}`,
      target: `node-${edgeTemplate.target}`,
      type: 'default',
    }));

    // Generate suggestions based on the pattern
    const suggestions = this.generateSuggestions(pattern, request.description);

    return {
      workflow: {
        nodes,
        edges,
        name: this.generateWorkflowName(request.description),
        description: request.description,
        status: 'draft',
        version: 1,
        tags: this.extractTags(request.description),
        isPublic: false,
        category: this.determineCategory(pattern),
        metadata: {
          executionCount: 0,
          successRate: 0,
          complexity: pattern.complexity,
          performance: {
            avgExecutionTime: 0,
            errorRate: 0,
            throughput: 0,
          },
        },
      },
      confidence: 0.85, // High confidence for pattern matches
      suggestions,
    };
  }

  private async generateWithAI(request: AIWorkflowRequest): Promise<AIWorkflowResponse> {
    try {
      // Use Hugging Face's free inference API for text generation
      const prompt = this.buildPrompt(request);
      
      const response = await axios.post(
        `${this.huggingFaceBaseUrl}/microsoft/DialoGPT-medium`,
        {
          inputs: prompt,
          parameters: {
            max_length: 500,
            temperature: 0.7,
            do_sample: true,
          },
        },
        {
          headers: {
            'Authorization': `Bearer ${this.huggingFaceApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      const aiResponse: HuggingFaceResponse[] = response.data;
      const generatedText = aiResponse[0]?.generated_text || '';

      // Parse the AI response to extract workflow structure
      const workflow = this.parseAIResponse(generatedText, request);
      
      return {
        workflow,
        confidence: aiResponse[0]?.score || 0.6,
        suggestions: this.generateGenericSuggestions(request.description),
      };

    } catch (error) {
      logger.error('AI model request failed:', error);
      
      // Fallback to a generic workflow
      return this.generateFallbackWorkflow(request);
    }
  }

  private buildPrompt(request: AIWorkflowRequest): string {
    return `Create a workflow for: ${request.description}

Please describe a step-by-step workflow that includes:
1. What triggers the workflow
2. What actions need to be performed
3. Any conditions or decision points
4. The final outcome

Workflow steps:`;
  }

  private parseAIResponse(aiText: string, request: AIWorkflowRequest): Partial<any> {
    // Simple parsing logic - in production, this would be more sophisticated
    const lines = aiText.split('\n').filter(line => line.trim());
    const nodes: WorkflowNode[] = [];
    const edges: WorkflowEdge[] = [];

    lines.forEach((line, index) => {
      if (line.includes('trigger') || line.includes('start')) {
        nodes.push({
          id: `node-${index}`,
          type: 'trigger',
          position: { x: 100, y: 100 + (index * 100) },
          data: { label: line.trim(), config: {} },
        });
      } else if (line.includes('condition') || line.includes('if') || line.includes('check')) {
        nodes.push({
          id: `node-${index}`,
          type: 'condition',
          position: { x: 300, y: 100 + (index * 100) },
          data: { label: line.trim(), config: {} },
        });
      } else if (line.includes('email') || line.includes('send')) {
        nodes.push({
          id: `node-${index}`,
          type: 'email',
          position: { x: 500, y: 100 + (index * 100) },
          data: { label: line.trim(), config: {} },
        });
      } else {
        nodes.push({
          id: `node-${index}`,
          type: 'action',
          position: { x: 200 + (index * 150), y: 100 },
          data: { label: line.trim(), config: {} },
        });
      }

      // Connect sequential nodes
      if (index > 0) {
        edges.push({
          id: `edge-${index}`,
          source: `node-${index - 1}`,
          target: `node-${index}`,
          type: 'default',
        });
      }
    });

    return {
      nodes: nodes.slice(0, 6), // Limit to 6 nodes
      edges: edges.slice(0, 5), // Limit to 5 edges
      name: this.generateWorkflowName(request.description),
      description: request.description,
      status: 'draft',
      version: 1,
      tags: this.extractTags(request.description),
      isPublic: false,
      category: 'automation',
      metadata: {
        executionCount: 0,
        successRate: 0,
        complexity: request.preferences?.complexity || 'medium',
        performance: {
          avgExecutionTime: 0,
          errorRate: 0,
          throughput: 0,
        },
      },
    };
  }

  private generateFallbackWorkflow(request: AIWorkflowRequest): AIWorkflowResponse {
    const nodes: WorkflowNode[] = [
      {
        id: 'node-0',
        type: 'trigger',
        position: { x: 100, y: 100 },
        data: { label: 'Start Workflow', config: {} },
      },
      {
        id: 'node-1',
        type: 'action',
        position: { x: 300, y: 100 },
        data: { label: 'Process Request', config: {} },
      },
      {
        id: 'node-2',
        type: 'condition',
        position: { x: 500, y: 100 },
        data: { label: 'Check Result', config: {} },
      },
    ];

    const edges: WorkflowEdge[] = [
      { id: 'edge-0', source: 'node-0', target: 'node-1', type: 'default' },
      { id: 'edge-1', source: 'node-1', target: 'node-2', type: 'default' },
    ];

    return {
      workflow: {
        nodes,
        edges,
        name: this.generateWorkflowName(request.description),
        description: request.description,
        status: 'draft',
        version: 1,
        tags: this.extractTags(request.description),
        isPublic: false,
        category: 'automation',
        metadata: {
          executionCount: 0,
          successRate: 0,
          complexity: 'simple',
          performance: {
            avgExecutionTime: 0,
            errorRate: 0,
            throughput: 0,
          },
        },
      },
      confidence: 0.4, // Low confidence for fallback
      suggestions: [
        'Try being more specific about the trigger event',
        'Describe the expected outcome in detail',
        'Mention any specific tools or services to integrate',
      ],
    };
  }

  private generateWorkflowName(description: string): string {
    const words = description.split(' ').slice(0, 4);
    return words.map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') + ' Workflow';
  }

  private extractTags(description: string): string[] {
    const commonTags = ['automation', 'email', 'data', 'api', 'webhook', 'schedule', 'notification'];
    const lowerDescription = description.toLowerCase();
    
    return commonTags.filter(tag => lowerDescription.includes(tag));
  }

  private determineCategory(pattern: WorkflowPattern): string {
    if (pattern.keywords.some(k => ['email', 'notification'].includes(k))) return 'communications';
    if (pattern.keywords.some(k => ['data', 'database', 'csv'].includes(k))) return 'data-processing';
    if (pattern.keywords.some(k => ['api', 'webhook', 'integration'].includes(k))) return 'integrations';
    return 'automation';
  }

  private generateSuggestions(pattern: WorkflowPattern, description: string): string[] {
    const baseSuggestions = [
      'Add error handling nodes for better reliability',
      'Consider adding logging for debugging',
      'Add conditions to handle edge cases',
    ];

    if (pattern.complexity === 'simple') {
      baseSuggestions.push('This workflow could be enhanced with additional validation steps');
    } else if (pattern.complexity === 'complex') {
      baseSuggestions.push('Consider breaking this into smaller sub-workflows');
    }

    return baseSuggestions;
  }

  private generateGenericSuggestions(description: string): string[] {
    return [
      'Add input validation to ensure data quality',
      'Include error handling for robust execution',
      'Consider adding notifications for important events',
      'Add logging nodes for better monitoring',
    ];
  }
}

export const aiProcessor = new AIProcessor();
