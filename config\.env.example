# FlowForge AI - Environment Configuration Template
# Copy this file to .env.development or .env.production and update the values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
PYTHON_ENV=development
APP_NAME=FlowForge AI
APP_VERSION=1.0.0
APP_URL=http://localhost:3000

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
FRONTEND_PORT=3000
BACKEND_PORT=8000
AI_SERVICE_PORT=8001
HTTP_PORT=80
HTTPS_PORT=443

# Frontend URLs
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_APP_NAME=FlowForge AI
NEXT_PUBLIC_APP_VERSION=1.0.0

# Backend URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=flowforge_ai
POSTGRES_USER=flowforge
POSTGRES_PASSWORD=flowforge_password
DATABASE_URL=postgresql://flowforge:flowforge_password@localhost:5432/flowforge_ai

# Database Pool Settings
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password
REDIS_URL=redis://:redis_password@localhost:6379
REDIS_DB=0

# Redis Connection Pool
REDIS_POOL_MIN=1
REDIS_POOL_MAX=10

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Password Hashing
BCRYPT_ROUNDS=12

# Session Configuration
SESSION_SECRET=your-session-secret-change-this-in-production
SESSION_MAX_AGE=86400000

# CORS Settings
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_AUTH_MAX=5

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
FILE_UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,text/csv,application/json,application/xml

# Cloud Storage (Optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=flowforge-uploads

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# Email Service Provider
EMAIL_SERVICE=smtp
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Email Templates
EMAIL_FROM_NAME=FlowForge AI
EMAIL_FROM_ADDRESS=<EMAIL>

# SendGrid (Alternative)
SENDGRID_API_KEY=
SENDGRID_FROM_EMAIL=<EMAIL>

# =============================================================================
# AI SERVICE CONFIGURATION
# =============================================================================
# Hugging Face API (Free Tier)
HUGGING_FACE_API_KEY=your-hugging-face-api-key

# AI Service Settings
AI_MAX_WORKERS=2
AI_TIMEOUT=30
AI_MODEL_CACHE_DIR=./models
AI_DEFAULT_MODEL=microsoft/DialoGPT-medium

# OpenAI API (Optional - Paid)
OPENAI_API_KEY=
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000

# =============================================================================
# WORKFLOW EXECUTION
# =============================================================================
# Scheduler Configuration
SCHEDULER_INTERVAL=60000
MAX_CONCURRENT_JOBS=10
JOB_TIMEOUT=300000
JOB_RETRY_ATTEMPTS=3
JOB_RETRY_DELAY=5000

# Workflow Limits
MAX_WORKFLOW_NODES=100
MAX_WORKFLOW_EXECUTIONS_PER_HOUR=1000
MAX_EXECUTION_TIME=600000

# =============================================================================
# INTEGRATION SERVICES
# =============================================================================
# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret
WEBHOOK_TIMEOUT=10000
WEBHOOK_RETRY_ATTEMPTS=3

# Third-party API Keys
SLACK_BOT_TOKEN=
SLACK_SIGNING_SECRET=
DISCORD_BOT_TOKEN=
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
TWITTER_API_KEY=
TWITTER_API_SECRET=

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE_PATH=./logs
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Prometheus Monitoring
PROMETHEUS_PORT=9090
PROMETHEUS_METRICS_PATH=/metrics

# Grafana Dashboard
GRAFANA_PORT=3001
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin

# =============================================================================
# ELASTICSEARCH & KIBANA (ELK Stack)
# =============================================================================
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_HOST=localhost
KIBANA_PORT=5601
LOGSTASH_PORT=5044

# =============================================================================
# PAYMENT PROCESSING
# =============================================================================
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# PayPal Configuration
PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=
PAYPAL_MODE=sandbox

# =============================================================================
# ANALYTICS & TRACKING
# =============================================================================
# Google Analytics
GOOGLE_ANALYTICS_ID=
GOOGLE_TAG_MANAGER_ID=

# Mixpanel Analytics
MIXPANEL_TOKEN=

# Sentry Error Tracking
SENTRY_DSN=
SENTRY_ENVIRONMENT=development

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_SOCIAL_LOGIN=false
ENABLE_MARKETPLACE=true
ENABLE_AI_FEATURES=true
ENABLE_COLLABORATION=true
ENABLE_ANALYTICS=true
ENABLE_MONITORING=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Debug Configuration
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_CORS=true
ENABLE_SWAGGER=true

# Hot Reload
CHOKIDAR_USEPOLLING=false
WATCHPACK_POLLING=false

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
# SSL Configuration
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem
FORCE_HTTPS=false

# CDN Configuration
CDN_URL=
STATIC_FILES_URL=

# Cache Configuration
CACHE_TTL=3600
ENABLE_REDIS_CACHE=true
ENABLE_MEMORY_CACHE=false

# =============================================================================
# BACKUP & MAINTENANCE
# =============================================================================
# Database Backup
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=flowforge-backups

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=FlowForge AI is currently under maintenance. Please try again later.

# =============================================================================
# CUSTOM INTEGRATIONS
# =============================================================================
# Custom API Endpoints
CUSTOM_API_ENDPOINT_1=
CUSTOM_API_KEY_1=

# Webhook URLs
CUSTOM_WEBHOOK_URL_1=
CUSTOM_WEBHOOK_SECRET_1=

# =============================================================================
# NOTES
# =============================================================================
# 1. Never commit .env files to version control
# 2. Use strong, unique passwords and secrets in production
# 3. Regularly rotate API keys and secrets
# 4. Enable HTTPS in production environments
# 5. Configure proper firewall rules and network security
# 6. Monitor resource usage and set up alerts
# 7. Implement proper backup and disaster recovery procedures
