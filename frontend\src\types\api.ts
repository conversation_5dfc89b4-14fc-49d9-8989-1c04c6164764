// API response and request types for FlowForge AI
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
  timestamp: string;
  requestId: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  field?: string;
  stack?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  filters?: Record<string, any>;
}

// Workflow API types
export interface CreateWorkflowRequest {
  name: string;
  description?: string;
  category?: string;
  tags?: string[];
  isPublic?: boolean;
  templateId?: string;
}

export interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  nodes?: any[];
  edges?: any[];
  tags?: string[];
  isPublic?: boolean;
  status?: string;
}

export interface WorkflowListParams extends PaginationParams {
  category?: string;
  status?: string;
  tags?: string[];
  createdBy?: string;
  isPublic?: boolean;
}

export interface ExecuteWorkflowRequest {
  input?: Record<string, any>;
  triggerSource?: string;
  scheduledFor?: string;
}

// Template API types
export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: string;
  tags: string[];
  workflowId: string;
  price?: number;
  isPremium?: boolean;
  previewImage?: string;
  useCases: string[];
  requirements: string[];
}

export interface TemplateListParams extends PaginationParams {
  category?: string;
  tags?: string[];
  isPremium?: boolean;
  priceRange?: [number, number];
  rating?: number;
  author?: string;
}

// Marketplace API types
export interface MarketplaceStats {
  totalTemplates: number;
  totalDownloads: number;
  totalRevenue: number;
  topCategories: CategoryStats[];
  recentActivity: MarketplaceActivity[];
}

export interface CategoryStats {
  category: string;
  count: number;
  growth: number;
}

export interface MarketplaceActivity {
  id: string;
  type: 'download' | 'purchase' | 'review' | 'upload';
  templateId: string;
  templateName: string;
  userId: string;
  username: string;
  timestamp: string;
  amount?: number;
}

export interface PurchaseRequest {
  templateId: string;
  paymentMethodId?: string;
}

export interface ReviewRequest {
  templateId: string;
  rating: number;
  comment?: string;
}

// AI API types
export interface AIGenerateWorkflowRequest {
  description: string;
  context?: string;
  preferences?: {
    complexity?: 'simple' | 'medium' | 'complex';
    category?: string;
    includeErrorHandling?: boolean;
  };
}

export interface AISuggestionRequest {
  workflowId: string;
  currentNodes: any[];
  currentEdges: any[];
  context?: string;
}

export interface AIOptimizeRequest {
  workflowId: string;
  optimizationGoals: ('performance' | 'cost' | 'reliability' | 'simplicity')[];
}

// Collaboration API types
export interface CollaborationInviteRequest {
  workflowId: string;
  email: string;
  role: 'viewer' | 'editor' | 'admin';
  message?: string;
}

export interface CollaborationUpdateRequest {
  userId: string;
  role: 'viewer' | 'editor' | 'admin';
}

export interface WorkflowComment {
  id: string;
  workflowId: string;
  userId: string;
  user: {
    id: string;
    username: string;
    avatar?: string;
  };
  content: string;
  position?: { x: number; y: number };
  nodeId?: string;
  parentId?: string;
  replies?: WorkflowComment[];
  createdAt: string;
  updatedAt: string;
  isResolved: boolean;
}

export interface CreateCommentRequest {
  content: string;
  position?: { x: number; y: number };
  nodeId?: string;
  parentId?: string;
}

// Analytics API types
export interface WorkflowAnalytics {
  workflowId: string;
  period: AnalyticsPeriod;
  metrics: {
    executions: ExecutionMetrics;
    performance: PerformanceMetrics;
    errors: ErrorMetrics;
    usage: UsageMetrics;
  };
}

export type AnalyticsPeriod = '1h' | '24h' | '7d' | '30d' | '90d' | '1y';

export interface ExecutionMetrics {
  total: number;
  successful: number;
  failed: number;
  cancelled: number;
  successRate: number;
  trend: DataPoint[];
}

export interface PerformanceMetrics {
  averageExecutionTime: number;
  medianExecutionTime: number;
  p95ExecutionTime: number;
  throughput: number;
  trend: DataPoint[];
}

export interface ErrorMetrics {
  totalErrors: number;
  errorRate: number;
  topErrors: ErrorSummary[];
  errorsByNode: Record<string, number>;
}

export interface ErrorSummary {
  code: string;
  message: string;
  count: number;
  lastOccurred: string;
}

export interface UsageMetrics {
  totalNodes: number;
  mostUsedNodes: NodeUsage[];
  executionsByTrigger: Record<string, number>;
  peakUsageHours: number[];
}

export interface NodeUsage {
  nodeType: string;
  count: number;
  successRate: number;
  averageExecutionTime: number;
}

export interface DataPoint {
  timestamp: string;
  value: number;
}

// Integration API types
export interface IntegrationConnection {
  id: string;
  type: string;
  name: string;
  status: 'connected' | 'disconnected' | 'error';
  config: Record<string, any>;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateConnectionRequest {
  type: string;
  name: string;
  config: Record<string, any>;
  testConnection?: boolean;
}

export interface TestConnectionRequest {
  type: string;
  config: Record<string, any>;
}

export interface TestConnectionResponse {
  success: boolean;
  message: string;
  details?: Record<string, any>;
}

// Webhook API types
export interface WebhookEndpoint {
  id: string;
  workflowId: string;
  url: string;
  secret: string;
  events: string[];
  isActive: boolean;
  lastTriggered?: string;
  createdAt: string;
}

export interface CreateWebhookRequest {
  workflowId: string;
  events: string[];
  secret?: string;
}

export interface WebhookEvent {
  id: string;
  endpointId: string;
  event: string;
  payload: Record<string, any>;
  status: 'pending' | 'delivered' | 'failed';
  attempts: number;
  lastAttempt?: string;
  createdAt: string;
}

// File upload types
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  uploadedAt: string;
}

export interface FileUploadRequest {
  file: File;
  category?: string;
  isPublic?: boolean;
}

// Search API types
export interface SearchRequest {
  query: string;
  type?: 'workflows' | 'templates' | 'users' | 'all';
  filters?: Record<string, any>;
  limit?: number;
}

export interface SearchResponse {
  query: string;
  results: SearchResult[];
  suggestions: string[];
  totalResults: number;
}

export interface SearchResult {
  id: string;
  type: 'workflow' | 'template' | 'user';
  title: string;
  description: string;
  url: string;
  thumbnail?: string;
  relevanceScore: number;
  highlights: string[];
}
