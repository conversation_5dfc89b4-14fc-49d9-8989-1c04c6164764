'use client';

import React, { useCallback, useRef, useState, useEffect } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  Connection,
  EdgeChange,
  NodeChange,
  ReactFlowProvider,
  ReactFlowInstance,
  Panel,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { WorkflowNode, WorkflowEdge, NodeType } from '@/types/workflow';
import { useWorkflow } from '@/hooks/useWorkflow';
import { NodeLibrary } from './NodeLibrary';
import { NodeEditor } from './NodeEditor';
import { WorkflowToolbar } from './WorkflowToolbar';
import { CollaborationPanel } from '../collaboration/CollaborationPanel';
import { AIChat } from '../ai/AIChat';
import { toast } from 'react-hot-toast';

interface WorkflowBuilderProps {
  workflowId?: string;
  initialNodes?: WorkflowNode[];
  initialEdges?: WorkflowEdge[];
  readOnly?: boolean;
  showCollaboration?: boolean;
  showAI?: boolean;
  className?: string;
}

const nodeTypes = {
  trigger: React.lazy(() => import('./nodes/TriggerNode')),
  action: React.lazy(() => import('./nodes/ActionNode')),
  condition: React.lazy(() => import('./nodes/ConditionNode')),
  loop: React.lazy(() => import('./nodes/LoopNode')),
  delay: React.lazy(() => import('./nodes/DelayNode')),
  webhook: React.lazy(() => import('./nodes/WebhookNode')),
  email: React.lazy(() => import('./nodes/EmailNode')),
  database: React.lazy(() => import('./nodes/DatabaseNode')),
  api: React.lazy(() => import('./nodes/ApiNode')),
  transform: React.lazy(() => import('./nodes/TransformNode')),
  filter: React.lazy(() => import('./nodes/FilterNode')),
  merge: React.lazy(() => import('./nodes/MergeNode')),
  split: React.lazy(() => import('./nodes/SplitNode')),
  custom: React.lazy(() => import('./nodes/CustomNode')),
};

export const WorkflowBuilder: React.FC<WorkflowBuilderProps> = ({
  workflowId,
  initialNodes = [],
  initialEdges = [],
  readOnly = false,
  showCollaboration = true,
  showAI = true,
  className = '',
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [showNodeLibrary, setShowNodeLibrary] = useState(true);
  const [showNodeEditor, setShowNodeEditor] = useState(false);
  const [showCollaborationPanel, setShowCollaborationPanel] = useState(false);
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);

  const {
    workflow,
    saveWorkflow,
    executeWorkflow,
    isLoading,
    error,
  } = useWorkflow(workflowId);

  // Handle node connection
  const onConnect = useCallback(
    (params: Connection) => {
      if (readOnly) return;
      
      const newEdge: Edge = {
        ...params,
        id: `edge-${params.source}-${params.target}-${Date.now()}`,
        type: 'default',
        animated: false,
      };
      
      setEdges((eds) => addEdge(newEdge, eds));
      toast.success('Nodes connected successfully');
    },
    [setEdges, readOnly]
  );

  // Handle node selection
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    if (readOnly) return;
    
    setSelectedNode(node as WorkflowNode);
    setShowNodeEditor(true);
  }, [readOnly]);

  // Handle drag over for node library
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // Handle drop from node library
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      if (!reactFlowInstance || readOnly) return;

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!reactFlowBounds) return;

      const nodeType = event.dataTransfer.getData('application/reactflow');
      if (!nodeType) return;

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode: WorkflowNode = {
        id: `${nodeType}-${Date.now()}`,
        type: nodeType as NodeType,
        position,
        data: {
          label: `${nodeType.charAt(0).toUpperCase() + nodeType.slice(1)} Node`,
          config: {},
        },
      };

      setNodes((nds) => nds.concat(newNode));
      toast.success(`${nodeType} node added`);
    },
    [reactFlowInstance, setNodes, readOnly]
  );

  // Handle workflow execution
  const handleExecute = useCallback(async () => {
    if (!workflowId || isExecuting) return;

    setIsExecuting(true);
    try {
      await executeWorkflow({
        input: {},
        triggerSource: 'manual',
      });
      toast.success('Workflow executed successfully');
    } catch (error) {
      toast.error('Failed to execute workflow');
      console.error('Execution error:', error);
    } finally {
      setIsExecuting(false);
    }
  }, [workflowId, executeWorkflow, isExecuting]);

  // Handle workflow save
  const handleSave = useCallback(async () => {
    if (!workflowId || readOnly) return;

    try {
      await saveWorkflow({
        nodes: nodes as WorkflowNode[],
        edges: edges as WorkflowEdge[],
      });
      toast.success('Workflow saved successfully');
    } catch (error) {
      toast.error('Failed to save workflow');
      console.error('Save error:', error);
    }
  }, [workflowId, nodes, edges, saveWorkflow, readOnly]);

  // Auto-save functionality
  useEffect(() => {
    if (!workflowId || readOnly) return;

    const autoSaveTimer = setTimeout(() => {
      handleSave();
    }, 5000); // Auto-save every 5 seconds

    return () => clearTimeout(autoSaveTimer);
  }, [nodes, edges, handleSave, workflowId, readOnly]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (readOnly) return;

      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            handleSave();
            break;
          case 'r':
            event.preventDefault();
            handleExecute();
            break;
          case 'z':
            event.preventDefault();
            // TODO: Implement undo functionality
            break;
          case 'y':
            event.preventDefault();
            // TODO: Implement redo functionality
            break;
        }
      }

      if (event.key === 'Delete' && selectedNode) {
        setNodes((nds) => nds.filter((n) => n.id !== selectedNode.id));
        setEdges((eds) => eds.filter((e) => e.source !== selectedNode.id && e.target !== selectedNode.id));
        setSelectedNode(null);
        setShowNodeEditor(false);
        toast.success('Node deleted');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleSave, handleExecute, selectedNode, setNodes, setEdges, readOnly]);

  return (
    <div className={`flex h-full bg-gray-50 ${className}`}>
      {/* Node Library Sidebar */}
      {showNodeLibrary && !readOnly && (
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          <NodeLibrary onNodeDrag={() => {}} />
        </div>
      )}

      {/* Main Workflow Canvas */}
      <div className="flex-1 relative">
        <ReactFlowProvider>
          <div ref={reactFlowWrapper} className="w-full h-full">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              onDrop={onDrop}
              onDragOver={onDragOver}
              onInit={setReactFlowInstance}
              nodeTypes={nodeTypes}
              fitView
              attributionPosition="bottom-left"
              className="bg-gray-50"
            >
              <Controls />
              <MiniMap />
              <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
              
              {/* Workflow Toolbar */}
              <Panel position="top-left">
                <WorkflowToolbar
                  onSave={handleSave}
                  onExecute={handleExecute}
                  onToggleNodeLibrary={() => setShowNodeLibrary(!showNodeLibrary)}
                  onToggleCollaboration={() => setShowCollaborationPanel(!showCollaborationPanel)}
                  onToggleAI={() => setShowAIPanel(!showAIPanel)}
                  isExecuting={isExecuting}
                  isSaving={isLoading}
                  readOnly={readOnly}
                  showCollaboration={showCollaboration}
                  showAI={showAI}
                />
              </Panel>
            </ReactFlow>
          </div>
        </ReactFlowProvider>
      </div>

      {/* Node Editor Panel */}
      {showNodeEditor && selectedNode && !readOnly && (
        <div className="w-96 bg-white border-l border-gray-200">
          <NodeEditor
            node={selectedNode}
            onClose={() => {
              setShowNodeEditor(false);
              setSelectedNode(null);
            }}
            onUpdate={(updatedNode) => {
              setNodes((nds) =>
                nds.map((n) => (n.id === updatedNode.id ? updatedNode : n))
              );
            }}
          />
        </div>
      )}

      {/* Collaboration Panel */}
      {showCollaborationPanel && showCollaboration && workflowId && (
        <div className="w-80 bg-white border-l border-gray-200">
          <CollaborationPanel
            workflowId={workflowId}
            onClose={() => setShowCollaborationPanel(false)}
          />
        </div>
      )}

      {/* AI Assistant Panel */}
      {showAIPanel && showAI && (
        <div className="w-96 bg-white border-l border-gray-200">
          <AIChat
            workflowId={workflowId}
            currentNodes={nodes as WorkflowNode[]}
            currentEdges={edges as WorkflowEdge[]}
            onWorkflowGenerated={(generatedWorkflow) => {
              setNodes(generatedWorkflow.nodes || []);
              setEdges(generatedWorkflow.edges || []);
              toast.success('AI workflow generated successfully');
            }}
            onClose={() => setShowAIPanel(false)}
          />
        </div>
      )}
    </div>
  );
};
