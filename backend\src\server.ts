import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import 'express-async-errors';

import { app } from './app';
import { logger } from './utils/logger';
import { connectDatabase } from './utils/database';
import { connectRedis } from './config/redis';
import { initializeQueues } from './services/queueService';
import { setupSocketHandlers } from './services/websocketService';

// Load environment variables
dotenv.config();

const PORT = process.env.PORT || 8000;
const NODE_ENV = process.env.NODE_ENV || 'development';

async function startServer() {
  try {
    // Initialize database connection
    logger.info('Connecting to database...');
    await connectDatabase();
    logger.info('Database connected successfully');

    // Initialize Redis connection
    logger.info('Connecting to Redis...');
    await connectRedis();
    logger.info('Redis connected successfully');

    // Initialize job queues
    logger.info('Initializing job queues...');
    await initializeQueues();
    logger.info('Job queues initialized successfully');

    // Create HTTP server
    const server = createServer(app);

    // Initialize Socket.IO
    const io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    // Setup Socket.IO handlers
    setupSocketHandlers(io);

    // Make io available to routes
    app.set('io', io);

    // Start server
    server.listen(PORT, () => {
      logger.info(`🚀 FlowForge AI Backend Server started`);
      logger.info(`📍 Environment: ${NODE_ENV}`);
      logger.info(`🌐 Server running on port ${PORT}`);
      logger.info(`📡 Socket.IO enabled for real-time features`);
      
      if (NODE_ENV === 'development') {
        logger.info(`🔗 API Documentation: http://localhost:${PORT}/api/docs`);
        logger.info(`🎯 Health Check: http://localhost:${PORT}/api/health`);
      }
    });

    // Graceful shutdown handling
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      server.close(async () => {
        logger.info('HTTP server closed');
        
        try {
          // Close database connections
          await disconnectDatabase();
          logger.info('Database disconnected');
          
          // Close Redis connection
          await disconnectRedis();
          logger.info('Redis disconnected');
          
          // Close job queues
          await closeQueues();
          logger.info('Job queues closed');
          
          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          logger.error('Error during graceful shutdown:', error);
          process.exit(1);
        }
      });
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Import cleanup functions (these would be implemented in their respective modules)
async function disconnectDatabase() {
  // TODO: Implement database disconnection
  const { PrismaClient } = await import('@prisma/client');
  const prisma = new PrismaClient();
  await prisma.$disconnect();
}

async function disconnectRedis() {
  // TODO: Implement Redis disconnection
  const { redisClient } = await import('./config/redis');
  if (redisClient.isOpen) {
    await redisClient.quit();
  }
}

async function closeQueues() {
  // TODO: Implement queue cleanup
  const { closeAllQueues } = await import('./services/queueService');
  await closeAllQueues();
}

// Start the server
if (require.main === module) {
  startServer();
}

export { startServer };
