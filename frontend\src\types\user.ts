// User and authentication types for FlowForge AI
export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: UserRole;
  plan: SubscriptionPlan;
  preferences: UserPreferences;
  stats: UserStats;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  isEmailVerified: boolean;
  isActive: boolean;
}

export type UserRole = 'user' | 'admin' | 'moderator' | 'enterprise';

export interface SubscriptionPlan {
  id: string;
  name: string;
  type: PlanType;
  features: PlanFeature[];
  limits: PlanLimits;
  price: number;
  billingCycle: 'monthly' | 'yearly';
  isActive: boolean;
  expiresAt?: string;
  trialEndsAt?: string;
}

export type PlanType = 'free' | 'starter' | 'professional' | 'enterprise' | 'custom';

export interface PlanFeature {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  limit?: number;
}

export interface PlanLimits {
  maxWorkflows: number;
  maxExecutionsPerMonth: number;
  maxNodesPerWorkflow: number;
  maxCollaborators: number;
  maxTemplates: number;
  maxFileSize: number; // in MB
  maxApiCalls: number;
  hasAdvancedAnalytics: boolean;
  hasPrioritySupport: boolean;
  hasWhiteLabel: boolean;
  hasCustomIntegrations: boolean;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: NotificationSettings;
  workflow: WorkflowPreferences;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  email: {
    workflowFailures: boolean;
    weeklyReports: boolean;
    marketplaceUpdates: boolean;
    collaborationInvites: boolean;
    systemUpdates: boolean;
  };
  push: {
    workflowFailures: boolean;
    collaborationActivity: boolean;
    marketplaceActivity: boolean;
  };
  inApp: {
    workflowFailures: boolean;
    collaborationActivity: boolean;
    systemMessages: boolean;
  };
}

export interface WorkflowPreferences {
  defaultView: 'canvas' | 'list' | 'grid';
  autoSave: boolean;
  autoSaveInterval: number; // in seconds
  showMinimap: boolean;
  snapToGrid: boolean;
  gridSize: number;
  defaultNodeStyle: {
    color: string;
    borderRadius: number;
    fontSize: number;
  };
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'friends';
  workflowVisibility: 'public' | 'private' | 'unlisted';
  allowAnalytics: boolean;
  allowMarketing: boolean;
  showOnlineStatus: boolean;
}

export interface UserStats {
  totalWorkflows: number;
  totalExecutions: number;
  totalTemplatesCreated: number;
  totalTemplateDownloads: number;
  totalRevenue: number;
  averageWorkflowComplexity: number;
  joinedAt: string;
  lastActiveAt: string;
  achievements: Achievement[];
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

// Authentication types
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Team and collaboration types
export interface Team {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  ownerId: string;
  members: TeamMember[];
  settings: TeamSettings;
  createdAt: string;
  updatedAt: string;
}

export interface TeamMember {
  userId: string;
  user: Pick<User, 'id' | 'username' | 'firstName' | 'lastName' | 'avatar'>;
  role: TeamRole;
  permissions: TeamPermission[];
  joinedAt: string;
  invitedBy: string;
  isActive: boolean;
}

export type TeamRole = 'owner' | 'admin' | 'editor' | 'viewer';

export interface TeamPermission {
  resource: 'workflows' | 'templates' | 'settings' | 'members' | 'billing';
  actions: ('create' | 'read' | 'update' | 'delete' | 'share')[];
}

export interface TeamSettings {
  defaultWorkflowVisibility: 'private' | 'team' | 'public';
  allowMemberInvites: boolean;
  requireApprovalForPublicWorkflows: boolean;
  maxWorkflowsPerMember: number;
}

export interface TeamInvitation {
  id: string;
  teamId: string;
  team: Pick<Team, 'id' | 'name' | 'avatar'>;
  email: string;
  role: TeamRole;
  invitedBy: string;
  inviter: Pick<User, 'id' | 'username' | 'firstName' | 'lastName'>;
  expiresAt: string;
  createdAt: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
}

// Billing types
export interface BillingInfo {
  customerId: string;
  subscriptionId?: string;
  paymentMethod?: PaymentMethod;
  billingAddress: Address;
  taxId?: string;
  invoices: Invoice[];
  upcomingInvoice?: Invoice;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank' | 'paypal';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

export interface Address {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface Invoice {
  id: string;
  number: string;
  amount: number;
  currency: string;
  status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible';
  dueDate: string;
  paidAt?: string;
  downloadUrl?: string;
  items: InvoiceItem[];
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}
