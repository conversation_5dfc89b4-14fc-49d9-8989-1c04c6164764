{"name": "flowforge-ai-backend", "version": "1.0.0", "description": "FlowForge AI - Visual Workflow Automation Backend API", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"express": "^4.18.0", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "express-rate-limit": "^7.1.0", "express-validator": "^7.0.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.0", "nodemailer": "^6.9.0", "socket.io": "^4.7.0", "redis": "^4.6.0", "bull": "^4.12.0", "cron": "^3.1.0", "axios": "^1.6.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.0", "@prisma/client": "^5.7.0", "prisma": "^5.7.0", "zod": "^3.22.0", "joi": "^17.11.0", "lodash": "^4.17.21", "uuid": "^9.0.0", "moment": "^2.29.0", "csv-parser": "^3.0.0", "xml2js": "^0.6.0", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.6.0", "node-cron": "^3.0.0", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/helmet": "^4.0.0", "@types/compression": "^1.7.0", "@types/morgan": "^1.9.0", "@types/bcryptjs": "^2.4.0", "@types/jsonwebtoken": "^9.0.0", "@types/passport": "^1.0.0", "@types/passport-jwt": "^3.0.0", "@types/passport-local": "^1.0.0", "@types/multer": "^1.4.0", "@types/nodemailer": "^6.4.0", "@types/bull": "^4.10.0", "@types/cron": "^2.0.0", "@types/lodash": "^4.14.0", "@types/uuid": "^9.0.0", "@types/xml2js": "^0.4.0", "@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.0", "nodemon": "^3.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "jest": "^29.7.0", "@types/jest": "^29.5.0", "ts-jest": "^29.1.0", "supertest": "^6.3.0", "@types/supertest": "^2.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["workflow", "automation", "api", "nodejs", "typescript", "express"], "author": "FlowForge AI Team", "license": "MIT"}