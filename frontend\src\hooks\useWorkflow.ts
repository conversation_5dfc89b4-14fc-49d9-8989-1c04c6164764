'use client';

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Workflow, 
  WorkflowNode, 
  WorkflowEdge, 
  WorkflowExecution,
  ExecuteWorkflowRequest,
  UpdateWorkflowRequest 
} from '@/types/workflow';
import { workflowService } from '@/services/workflowService';
import { toast } from 'react-hot-toast';

interface UseWorkflowOptions {
  autoSave?: boolean;
  autoSaveDelay?: number;
}

interface UseWorkflowReturn {
  // Data
  workflow: Workflow | null;
  executions: WorkflowExecution[];
  isLoading: boolean;
  isSaving: boolean;
  isExecuting: boolean;
  error: string | null;
  
  // Actions
  saveWorkflow: (updates: Partial<UpdateWorkflowRequest>) => Promise<void>;
  executeWorkflow: (request: ExecuteWorkflowRequest) => Promise<WorkflowExecution>;
  duplicateWorkflow: () => Promise<Workflow>;
  deleteWorkflow: () => Promise<void>;
  exportWorkflow: (format: 'json' | 'yaml') => Promise<string>;
  importWorkflow: (data: string, format: 'json' | 'yaml') => Promise<void>;
  
  // Real-time updates
  subscribeToUpdates: () => void;
  unsubscribeFromUpdates: () => void;
  
  // Validation
  validateWorkflow: () => Promise<{ isValid: boolean; errors: string[] }>;
  
  // History
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

export const useWorkflow = (
  workflowId?: string,
  options: UseWorkflowOptions = {}
): UseWorkflowReturn => {
  const { autoSave = true, autoSaveDelay = 2000 } = options;
  
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);
  const [history, setHistory] = useState<Workflow[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [autoSaveTimeout, setAutoSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  // Fetch workflow data
  const {
    data: workflow,
    isLoading,
    error: queryError,
  } = useQuery({
    queryKey: ['workflow', workflowId],
    queryFn: () => workflowId ? workflowService.getWorkflow(workflowId) : null,
    enabled: !!workflowId,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  // Fetch executions
  const { data: executions = [] } = useQuery({
    queryKey: ['workflow-executions', workflowId],
    queryFn: () => workflowId ? workflowService.getWorkflowExecutions(workflowId) : [],
    enabled: !!workflowId,
    refetchInterval: 5000, // Refresh every 5 seconds
  });

  // Save workflow mutation
  const saveWorkflowMutation = useMutation({
    mutationFn: (updates: Partial<UpdateWorkflowRequest>) => {
      if (!workflowId) throw new Error('No workflow ID provided');
      return workflowService.updateWorkflow(workflowId, updates);
    },
    onSuccess: (updatedWorkflow) => {
      queryClient.setQueryData(['workflow', workflowId], updatedWorkflow);
      
      // Update history
      if (updatedWorkflow) {
        setHistory(prev => {
          const newHistory = prev.slice(0, historyIndex + 1);
          newHistory.push(updatedWorkflow);
          return newHistory.slice(-50); // Keep last 50 versions
        });
        setHistoryIndex(prev => Math.min(prev + 1, 49));
      }
      
      setError(null);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to save workflow';
      setError(message);
      toast.error(message);
    },
  });

  // Execute workflow mutation
  const executeWorkflowMutation = useMutation({
    mutationFn: (request: ExecuteWorkflowRequest) => {
      if (!workflowId) throw new Error('No workflow ID provided');
      return workflowService.executeWorkflow(workflowId, request);
    },
    onSuccess: (execution) => {
      queryClient.invalidateQueries({ queryKey: ['workflow-executions', workflowId] });
      toast.success('Workflow execution started');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to execute workflow';
      setError(message);
      toast.error(message);
    },
  });

  // Duplicate workflow mutation
  const duplicateWorkflowMutation = useMutation({
    mutationFn: () => {
      if (!workflowId) throw new Error('No workflow ID provided');
      return workflowService.duplicateWorkflow(workflowId);
    },
    onSuccess: (newWorkflow) => {
      queryClient.invalidateQueries({ queryKey: ['workflows'] });
      toast.success('Workflow duplicated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to duplicate workflow';
      setError(message);
      toast.error(message);
    },
  });

  // Delete workflow mutation
  const deleteWorkflowMutation = useMutation({
    mutationFn: () => {
      if (!workflowId) throw new Error('No workflow ID provided');
      return workflowService.deleteWorkflow(workflowId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workflows'] });
      queryClient.removeQueries({ queryKey: ['workflow', workflowId] });
      toast.success('Workflow deleted successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete workflow';
      setError(message);
      toast.error(message);
    },
  });

  // Auto-save functionality
  const scheduleAutoSave = useCallback((updates: Partial<UpdateWorkflowRequest>) => {
    if (!autoSave || !workflowId) return;

    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    const timeout = setTimeout(() => {
      saveWorkflowMutation.mutate(updates);
    }, autoSaveDelay);

    setAutoSaveTimeout(timeout);
  }, [autoSave, autoSaveDelay, workflowId, saveWorkflowMutation]);

  // Public API
  const saveWorkflow = useCallback(async (updates: Partial<UpdateWorkflowRequest>) => {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
      setAutoSaveTimeout(null);
    }
    
    return saveWorkflowMutation.mutateAsync(updates);
  }, [saveWorkflowMutation, autoSaveTimeout]);

  const executeWorkflow = useCallback(async (request: ExecuteWorkflowRequest) => {
    return executeWorkflowMutation.mutateAsync(request);
  }, [executeWorkflowMutation]);

  const duplicateWorkflow = useCallback(async () => {
    return duplicateWorkflowMutation.mutateAsync();
  }, [duplicateWorkflowMutation]);

  const deleteWorkflow = useCallback(async () => {
    return deleteWorkflowMutation.mutateAsync();
  }, [deleteWorkflowMutation]);

  const exportWorkflow = useCallback(async (format: 'json' | 'yaml') => {
    if (!workflow) throw new Error('No workflow to export');
    return workflowService.exportWorkflow(workflow.id, format);
  }, [workflow]);

  const importWorkflow = useCallback(async (data: string, format: 'json' | 'yaml') => {
    if (!workflowId) throw new Error('No workflow ID provided');
    
    const importedWorkflow = await workflowService.importWorkflow(data, format);
    
    // Update current workflow with imported data
    await saveWorkflow({
      nodes: importedWorkflow.nodes,
      edges: importedWorkflow.edges,
      name: importedWorkflow.name,
      description: importedWorkflow.description,
    });
  }, [workflowId, saveWorkflow]);

  const validateWorkflow = useCallback(async () => {
    if (!workflow) return { isValid: false, errors: ['No workflow to validate'] };
    return workflowService.validateWorkflow(workflow.id);
  }, [workflow]);

  const subscribeToUpdates = useCallback(() => {
    if (!workflowId) return;
    // TODO: Implement WebSocket subscription for real-time updates
    console.log('Subscribing to workflow updates:', workflowId);
  }, [workflowId]);

  const unsubscribeFromUpdates = useCallback(() => {
    if (!workflowId) return;
    // TODO: Implement WebSocket unsubscription
    console.log('Unsubscribing from workflow updates:', workflowId);
  }, [workflowId]);

  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const previousWorkflow = history[historyIndex - 1];
      setHistoryIndex(prev => prev - 1);
      
      saveWorkflow({
        nodes: previousWorkflow.nodes,
        edges: previousWorkflow.edges,
      });
    }
  }, [history, historyIndex, saveWorkflow]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const nextWorkflow = history[historyIndex + 1];
      setHistoryIndex(prev => prev + 1);
      
      saveWorkflow({
        nodes: nextWorkflow.nodes,
        edges: nextWorkflow.edges,
      });
    }
  }, [history, historyIndex, saveWorkflow]);

  // Initialize history when workflow loads
  useEffect(() => {
    if (workflow && history.length === 0) {
      setHistory([workflow]);
      setHistoryIndex(0);
    }
  }, [workflow, history.length]);

  // Handle query errors
  useEffect(() => {
    if (queryError) {
      const message = (queryError as any)?.response?.data?.message || 'Failed to load workflow';
      setError(message);
    }
  }, [queryError]);

  // Cleanup auto-save timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  }, [autoSaveTimeout]);

  return {
    // Data
    workflow,
    executions,
    isLoading,
    isSaving: saveWorkflowMutation.isPending,
    isExecuting: executeWorkflowMutation.isPending,
    error,
    
    // Actions
    saveWorkflow,
    executeWorkflow,
    duplicateWorkflow,
    deleteWorkflow,
    exportWorkflow,
    importWorkflow,
    
    // Real-time updates
    subscribeToUpdates,
    unsubscribeFromUpdates,
    
    // Validation
    validateWorkflow,
    
    // History
    undo,
    redo,
    canUndo: historyIndex > 0,
    canRedo: historyIndex < history.length - 1,
  };
};
