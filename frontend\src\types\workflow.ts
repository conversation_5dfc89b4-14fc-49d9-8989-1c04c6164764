// Core workflow types for FlowForge AI
export interface WorkflowNode {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
  selected?: boolean;
  dragging?: boolean;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  type?: EdgeType;
  animated?: boolean;
  style?: React.CSSProperties;
  data?: EdgeData;
}

export interface Workflow {
  id: string;
  name: string;
  description?: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  version: number;
  status: WorkflowStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  tags: string[];
  isPublic: boolean;
  category?: WorkflowCategory;
  metadata: WorkflowMetadata;
}

export type NodeType = 
  | 'trigger'
  | 'action'
  | 'condition'
  | 'loop'
  | 'delay'
  | 'webhook'
  | 'email'
  | 'database'
  | 'api'
  | 'transform'
  | 'filter'
  | 'merge'
  | 'split'
  | 'custom';

export type EdgeType = 'default' | 'straight' | 'step' | 'smoothstep' | 'bezier';

export type WorkflowStatus = 'draft' | 'active' | 'paused' | 'archived' | 'error';

export type WorkflowCategory = 
  | 'automation'
  | 'data-processing'
  | 'notifications'
  | 'integrations'
  | 'analytics'
  | 'marketing'
  | 'sales'
  | 'support'
  | 'finance'
  | 'hr'
  | 'custom';

export interface NodeData {
  label: string;
  description?: string;
  config: Record<string, any>;
  inputs?: NodeInput[];
  outputs?: NodeOutput[];
  icon?: string;
  color?: string;
  errors?: string[];
  warnings?: string[];
}

export interface NodeInput {
  id: string;
  name: string;
  type: DataType;
  required: boolean;
  defaultValue?: any;
  validation?: ValidationRule[];
}

export interface NodeOutput {
  id: string;
  name: string;
  type: DataType;
  description?: string;
}

export interface EdgeData {
  condition?: string;
  label?: string;
  color?: string;
}

export type DataType = 
  | 'string'
  | 'number'
  | 'boolean'
  | 'object'
  | 'array'
  | 'date'
  | 'file'
  | 'image'
  | 'json'
  | 'xml'
  | 'csv'
  | 'any';

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
}

export interface WorkflowMetadata {
  executionCount: number;
  lastExecuted?: string;
  averageExecutionTime?: number;
  successRate: number;
  estimatedCost?: number;
  complexity: 'simple' | 'medium' | 'complex';
  performance: {
    avgExecutionTime: number;
    errorRate: number;
    throughput: number;
  };
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: ExecutionStatus;
  startedAt: string;
  completedAt?: string;
  duration?: number;
  triggeredBy: TriggerSource;
  input?: Record<string, any>;
  output?: Record<string, any>;
  logs: ExecutionLog[];
  error?: ExecutionError;
}

export type ExecutionStatus = 
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'timeout';

export type TriggerSource = 
  | 'manual'
  | 'schedule'
  | 'webhook'
  | 'event'
  | 'api'
  | 'email'
  | 'file-upload';

export interface ExecutionLog {
  id: string;
  nodeId: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  data?: Record<string, any>;
  duration?: number;
}

export interface ExecutionError {
  code: string;
  message: string;
  nodeId?: string;
  stack?: string;
  context?: Record<string, any>;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: WorkflowCategory;
  tags: string[];
  workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>;
  author: string;
  downloads: number;
  rating: number;
  reviews: number;
  price?: number;
  isPremium: boolean;
  previewImage?: string;
  useCases: string[];
  requirements: string[];
}

export interface NodeLibraryItem {
  type: NodeType;
  name: string;
  description: string;
  icon: string;
  color: string;
  category: string;
  inputs: NodeInput[];
  outputs: NodeOutput[];
  configSchema: Record<string, any>;
  examples?: Record<string, any>[];
  documentation?: string;
}

// AI-related types
export interface AIWorkflowRequest {
  description: string;
  context?: string;
  preferences?: {
    complexity?: 'simple' | 'medium' | 'complex';
    category?: WorkflowCategory;
    includeErrorHandling?: boolean;
  };
}

export interface AIWorkflowResponse {
  workflow: Partial<Workflow>;
  confidence: number;
  suggestions: string[];
  alternatives?: Partial<Workflow>[];
}

export interface AINodeSuggestion {
  nodeType: NodeType;
  position: { x: number; y: number };
  config: Record<string, any>;
  confidence: number;
  reasoning: string;
}
